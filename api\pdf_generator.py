import os
import io
import base64
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from PIL import Image as PILImage

# Configuration des chemins
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
STATIC_DIR = os.path.join(BASE_DIR, '..', 'static')
LOGO_PATH = os.path.join(STATIC_DIR, 'img', 'logo.png')
FONTS_DIR = os.path.join(STATIC_DIR, 'fonts')

# Enregistrer la police arabe
try:
    arabic_font_path = os.path.join(FONTS_DIR, 'NotoSansArabic-Regular.ttf')
    if os.path.exists(arabic_font_path):
        pdfmetrics.registerFont(TTFont('NotoSansArabic', arabic_font_path))
        print("Police arabe chargée avec succès")
    else:
        print("Police arabe non trouvée, utilisation des polices par défaut")
except Exception as e:
    print(f"Erreur lors du chargement de la police arabe: {e}")

def create_pdf_report(data, image_data, site_name="MedScan AI"):
    """
    Créer un rapport PDF avec les résultats d'analyse en trois langues

    Args:
        data: Dictionnaire contenant les résultats d'analyse
        image_data: Image analysée en base64
        site_name: Nom du site à afficher dans l'en-tête

    Returns:
        Bytes du PDF généré
    """
    # Créer un buffer pour stocker le PDF
    buffer = io.BytesIO()

    # Créer le document PDF
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72,
        title=f"Rapport d'analyse médicale - {site_name}"
    )

    # Styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(
        name='RightAlign',
        fontName='Helvetica',
        fontSize=12,
        alignment=2,  # Alignement à droite
        leading=14
    ))

    # Style pour le texte arabe
    styles.add(ParagraphStyle(
        name='Arabic',
        fontName='NotoSansArabic' if 'NotoSansArabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
        fontSize=12,
        alignment=2,  # Alignement à droite
        leading=14
    ))

    # Éléments du document
    elements = []

    # En-tête avec logo et titre
    header_data = [
        [Image(LOGO_PATH, width=1.5*inch, height=0.75*inch) if os.path.exists(LOGO_PATH) else Paragraph(site_name, styles['Heading1']),
         Paragraph(f"<b>{site_name}</b><br/><font size=10>Rapport d'analyse médicale</font>", styles['Heading2'])]
    ]
    header_table = Table(header_data, colWidths=[doc.width/2.0]*2)
    header_table.setStyle(TableStyle([
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
    ]))
    elements.append(header_table)
    elements.append(Spacer(1, 20))

    # Date du rapport
    date_str = datetime.now().strftime("%d/%m/%Y %H:%M")
    elements.append(Paragraph(f"Date du rapport: {date_str}", styles['Normal']))
    elements.append(Spacer(1, 20))

    # Image analysée
    if image_data:
        try:
            # Nettoyer les données base64
            if ',' in image_data:
                image_data = image_data.split(',')[1]

            # Convertir en image
            img_data = base64.b64decode(image_data)
            img_stream = io.BytesIO(img_data)
            img = PILImage.open(img_stream)

            # Redimensionner l'image pour qu'elle tienne sur la page
            max_width = doc.width
            max_height = 3 * inch  # Limiter la hauteur

            width, height = img.size
            ratio = min(max_width/width, max_height/height)
            new_width = width * ratio
            new_height = height * ratio

            # Créer l'image pour le PDF
            img_stream.seek(0)
            img_obj = Image(img_stream, width=new_width, height=new_height)
            elements.append(Paragraph("Image analysée:", styles['Heading3']))
            elements.append(Spacer(1, 10))
            elements.append(img_obj)
            elements.append(Spacer(1, 20))
        except Exception as e:
            print(f"Erreur lors du traitement de l'image: {e}")
            elements.append(Paragraph("Erreur lors du chargement de l'image", styles['Normal']))
            elements.append(Spacer(1, 20))

    # Résultats en français (langue originale)
    elements.append(Paragraph("Résultats de l'analyse (Français):", styles['Heading3']))
    elements.append(Spacer(1, 10))

    # Classe détectée et confiance
    elements.append(Paragraph(f"<b>Classe détectée:</b> {data.get('class_name', 'Non disponible')}", styles['Normal']))
    elements.append(Paragraph(f"<b>Confiance:</b> {data.get('confidence', 0)}%", styles['Normal']))

    # Description
    if 'description' in data and data['description']:
        elements.append(Paragraph(f"<b>Description:</b> {data['description']}", styles['Normal']))

    # Analyse
    if 'analysis' in data and isinstance(data['analysis'], dict):
        if 'summary' in data['analysis'] and data['analysis']['summary']:
            elements.append(Paragraph(f"<b>Résumé:</b> {data['analysis']['summary']}", styles['Normal']))
        if 'recommendations' in data['analysis'] and data['analysis']['recommendations']:
            elements.append(Paragraph(f"<b>Recommandations:</b> {data['analysis']['recommendations']}", styles['Normal']))

    elements.append(Spacer(1, 20))

    # Obtenir les traductions en anglais et arabe
    from api.translation_service import translate_results

    # Traduction en anglais
    try:
        english_data = translate_results(data, 'en')
        elements.append(Paragraph("Analysis Results (English):", styles['Heading3']))
        elements.append(Spacer(1, 10))

        elements.append(Paragraph(f"<b>Detected Class:</b> {english_data.get('class_name', 'Not available')}", styles['Normal']))
        elements.append(Paragraph(f"<b>Confidence:</b> {english_data.get('confidence', 0)}%", styles['Normal']))

        if 'description' in english_data and english_data['description']:
            elements.append(Paragraph(f"<b>Description:</b> {english_data['description']}", styles['Normal']))

        if 'analysis' in english_data and isinstance(english_data['analysis'], dict):
            if 'summary' in english_data['analysis'] and english_data['analysis']['summary']:
                elements.append(Paragraph(f"<b>Summary:</b> {english_data['analysis']['summary']}", styles['Normal']))
            if 'recommendations' in english_data['analysis'] and english_data['analysis']['recommendations']:
                elements.append(Paragraph(f"<b>Recommendations:</b> {english_data['analysis']['recommendations']}", styles['Normal']))

        elements.append(Spacer(1, 20))
    except Exception as e:
        print(f"Erreur lors de la traduction en anglais: {e}")
        elements.append(Paragraph("English translation not available", styles['Normal']))
        elements.append(Spacer(1, 20))

    # Traduction en arabe
    try:
        arabic_data = translate_results(data, 'ar')
        elements.append(Paragraph("نتائج التحليل (العربية):", styles['Heading3']))
        elements.append(Spacer(1, 10))

        # Utiliser le style arabe avec la police arabe
        elements.append(Paragraph(f"<b>الفئة المكتشفة:</b> {arabic_data.get('class_name', 'غير متوفر')}", styles['Arabic']))
        elements.append(Paragraph(f"<b>الثقة:</b> {arabic_data.get('confidence', 0)}%", styles['Arabic']))

        if 'description' in arabic_data and arabic_data['description']:
            elements.append(Paragraph(f"<b>الوصف:</b> {arabic_data['description']}", styles['Arabic']))

        if 'analysis' in arabic_data and isinstance(arabic_data['analysis'], dict):
            if 'summary' in arabic_data['analysis'] and arabic_data['analysis']['summary']:
                elements.append(Paragraph(f"<b>ملخص:</b> {arabic_data['analysis']['summary']}", styles['Arabic']))
            if 'recommendations' in arabic_data['analysis'] and arabic_data['analysis']['recommendations']:
                elements.append(Paragraph(f"<b>توصيات:</b> {arabic_data['analysis']['recommendations']}", styles['Arabic']))
    except Exception as e:
        print(f"Erreur lors de la traduction en arabe: {e}")
        elements.append(Paragraph("الترجمة العربية غير متوفرة", styles['Normal']))

    # Construire le document
    doc.build(elements)

    # Récupérer le contenu du buffer
    pdf_data = buffer.getvalue()
    buffer.close()

    return pdf_data
