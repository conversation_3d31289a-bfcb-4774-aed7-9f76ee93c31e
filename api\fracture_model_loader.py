import os
import json
import numpy as np
from PIL import Image
import tensorflow as tf
from tensorflow.keras.models import load_model

# Chemin absolu sécurisé
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_PATH = os.path.join(BASE_DIR, 'models', 'fracture_classifier_model.h5')
LABELS_PATH = os.path.join(BASE_DIR, 'models', 'fracture_labels.json')

# Vérification des fichiers
if not os.path.exists(MODEL_PATH):
    print(f"Avertissement: Modèle de fracture introuvable à l'emplacement: {MODEL_PATH}")
    print("Le modèle sera chargé à la demande.")

if not os.path.exists(LABELS_PATH):
    # Créer le fichier de labels par défaut
    fracture_labels = {
        "0": {"name": "Normal", "description": "Radiographie ne présentant aucune fracture osseuse visible. L'os apparaît intact et sans ligne de fracture."},
        "1": {"name": "Fracture", "description": "Radiographie présentant une fracture osseuse. Une rupture ou fissure est visible dans la continuité de l'os."}
    }

    os.makedirs(os.path.dirname(LABELS_PATH), exist_ok=True)
    with open(LABELS_PATH, 'w') as f:
        json.dump(fracture_labels, f, indent=4)
    print(f"Fichier de labels créé à: {LABELS_PATH}")

# Variable pour stocker le modèle chargé
fracture_model = None

def load_fracture_model():
    """
    Charge le modèle de classification de fractures
    """
    global fracture_model

    # Si le modèle est déjà chargé, le retourner
    if fracture_model is not None:
        return fracture_model

    # Vérifier si le fichier du modèle existe
    if os.path.exists(MODEL_PATH):
        try:
            # Configurer TensorFlow pour utiliser moins de mémoire
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                try:
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                except RuntimeError as e:
                    print(f"GPU memory config error: {e}")

            # Charger le modèle TensorFlow avec des options de chargement sécurisées
            print(f"Tentative de chargement du modèle de fracture depuis: {MODEL_PATH}")
            fracture_model = load_model(MODEL_PATH, compile=False)
            # Compiler le modèle après chargement
            fracture_model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            print("Modèle de fracture chargé avec succès.")
        except Exception as e:
            print(f"Erreur lors du chargement du modèle de fracture: {e}")
            # Créer un modèle vide en cas d'erreur
            print("Création d'un modèle de secours simple...")
            fracture_model = tf.keras.Sequential([
                tf.keras.layers.Input(shape=(224, 224, 3)),
                tf.keras.layers.GlobalAveragePooling2D(),
                tf.keras.layers.Dense(1, activation='sigmoid')
            ])
            fracture_model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
    else:
        print(f"Modèle de fracture non trouvé à l'emplacement: {MODEL_PATH}")
        print("Utilisation d'un modèle non entraîné.")
        # Créer un modèle vide
        fracture_model = tf.keras.Sequential([
            tf.keras.layers.Input(shape=(224, 224, 3)),
            tf.keras.layers.GlobalAveragePooling2D(),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        fracture_model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

    return fracture_model

# Charger les labels
with open(LABELS_PATH) as f:
    fracture_labels = json.load(f)

def preprocess_image(pil_image):
    """
    Prétraite l'image pour le modèle de fracture
    """
    try:
        # Redimensionner l'image
        img = pil_image.resize((224, 224))

        # Convertir en tableau numpy avec gestion des erreurs
        img_array = np.array(img)

        # Vérifier le nombre de canaux et ajuster si nécessaire
        if len(img_array.shape) == 2:  # Image en niveaux de gris
            # Convertir en RGB en dupliquant le canal
            img_array = np.stack((img_array,) * 3, axis=-1)
        elif img_array.shape[2] == 4:  # Image avec canal alpha
            # Supprimer le canal alpha
            img_array = img_array[:, :, :3]

        # Normaliser les valeurs entre 0 et 1
        img_array = img_array.astype('float32') / 255.0

        # Ajouter une dimension pour le batch
        img_array = np.expand_dims(img_array, axis=0)

        # Vérifier la forme finale
        print(f"Forme de l'image prétraitée: {img_array.shape}")

        return img_array
    except Exception as e:
        print(f"Erreur lors du prétraitement de l'image: {e}")
        # Créer un tableau vide en cas d'erreur
        return np.zeros((1, 224, 224, 3), dtype='float32')

def predict_fracture_image(pil_image):
    """
    Predict if an X-ray image shows a fracture using the loaded model

    Args:
        pil_image: PIL Image object or path to image file

    Returns:
        dict: Prediction results with class, class_name, description, and confidence
    """
    try:
        # Charger le modèle à la demande
        global fracture_model
        if fracture_model is None:
            fracture_model = load_fracture_model()

        # If pil_image is a string (file path), open the image
        if isinstance(pil_image, str):
            pil_image = Image.open(pil_image).convert('RGB')
        elif not isinstance(pil_image, Image.Image):
            raise TypeError("Input must be a PIL Image or a file path")

        # Ensure image is in RGB mode
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # Prétraiter l'image
        img_array = preprocess_image(pil_image)

        # Faire la prédiction avec gestion des erreurs
        try:
            # Utiliser un timeout pour éviter les blocages
            prediction = fracture_model.predict(img_array, verbose=0)[0][0]
            print(f"Fracture prediction raw value: {prediction}")
        except Exception as e:
            print(f"Error during prediction: {e}")
            # Utiliser une valeur par défaut en cas d'erreur
            prediction = 0.0

        # Déterminer la classe (0: Normal, 1: Fracture)
        # Pour un modèle binaire avec activation sigmoid, nous utilisons un seuil de 0.5
        class_idx = 1 if prediction >= 0.5 else 0
        confidence = float(prediction) if class_idx == 1 else float(1 - prediction)

        # Get class information
        class_info = fracture_labels.get(str(class_idx), {
            'name': f'Classe {class_idx}',
            'description': 'Aucune description disponible'
        })

        return {
            'class': class_idx,
            'class_name': class_info['name'],
            'description': class_info['description'],
            'confidence': round(float(confidence) * 100, 2),
            'model_type': 'fracture'  # Indiquer le type de modèle utilisé
        }
    except Exception as e:
        print(f"Critical error in predict_fracture_image: {e}")
        # Retourner un résultat par défaut en cas d'erreur critique
        return {
            'class': 0,
            'class_name': 'Normal (par défaut)',
            'description': 'Résultat par défaut en raison d\'une erreur de traitement.',
            'confidence': 50.0,
            'model_type': 'fracture'
        }
