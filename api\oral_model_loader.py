import os
import torch
import json
from torchvision import transforms
import torch.nn as nn
from PIL import Image
from torchvision.models import resnet18

# Chemin absolu sécurisé
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_PATH = os.path.join(BASE_DIR, 'models', 'oral_model.pth')
LABELS_PATH = os.path.join(BASE_DIR, 'models', 'oral_labels.json')

# Vérification des fichiers
if not os.path.exists(MODEL_PATH):
    print(f"Avertissement: Modèle oral introuvable à l'emplacement: {MODEL_PATH}")
    print("Le modèle sera chargé à la demande.")

if not os.path.exists(LABELS_PATH):
    # Créer le fichier de labels par défaut
    oral_labels = {
        "0": {"name": "Calculus", "description": "Dépôts calcifiés sur les dents, également connus sous le nom de tartre."},
        "1": {"name": "Caries", "description": "Lésions dentaires causées par des bactéries, entraînant la destruction des tissus dentaires."},
        "2": {"name": "Gingivitis", "description": "Inflammation des gencives, souvent causée par une accumulation de plaque."},
        "3": {"name": "Ulcers", "description": "Lésions ouvertes dans la bouche, souvent douloureuses et peuvent être causées par diverses conditions."},
        "4": {"name": "Tooth Discoloration", "description": "Changement de couleur des dents, pouvant être causé par des facteurs externes ou internes."},
        "5": {"name": "Hypodontia", "description": "Absence congénitale d'une ou plusieurs dents."}
    }
    
    os.makedirs(os.path.dirname(LABELS_PATH), exist_ok=True)
    with open(LABELS_PATH, 'w') as f:
        json.dump(oral_labels, f, indent=4)
    print(f"Fichier de labels créé à: {LABELS_PATH}")

# Définition du modèle (basé sur ResNet18)
class OralDiseaseModel(nn.Module):
    def __init__(self, num_classes=6):
        super(OralDiseaseModel, self).__init__()
        # Utiliser ResNet18 comme base
        self.model = resnet18(weights=None)
        # Remplacer la dernière couche fully connected pour notre classification
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        return self.model(x)

# Variable pour stocker le modèle chargé
oral_model = None
device = None

def load_oral_model():
    """
    Charge le modèle de classification des maladies orales
    """
    global oral_model, device
    
    # Si le modèle est déjà chargé, le retourner
    if oral_model is not None:
        return oral_model, device
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    oral_model = OralDiseaseModel(num_classes=6)
    
    # Vérifier si le fichier du modèle existe
    if os.path.exists(MODEL_PATH):
        try:
            oral_model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
            print("Modèle oral chargé avec succès.")
        except Exception as e:
            print(f"Erreur lors du chargement du modèle oral: {e}")
    else:
        print("Modèle oral non trouvé. Utilisation d'un modèle non entraîné.")
    
    oral_model.eval()
    return oral_model, device

# Charger les labels
with open(LABELS_PATH) as f:
    oral_labels = json.load(f)

# Transformation pour les images
oral_transform = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

def predict_oral_image(pil_image):
    """
    Predict the class of an oral disease image using the loaded model
    
    Args:
        pil_image: PIL Image object or path to image file
    
    Returns:
        dict: Prediction results with class, class_name, description, and confidence
    """
    # Charger le modèle à la demande
    global oral_model, device
    if oral_model is None:
        oral_model, device = load_oral_model()
    
    # If pil_image is a string (file path), open the image
    if isinstance(pil_image, str):
        pil_image = Image.open(pil_image).convert('RGB')
    elif not isinstance(pil_image, Image.Image):
        raise TypeError("Input must be a PIL Image or a file path")
    
    # Ensure image is in RGB mode
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    # Apply transformations and get prediction
    image = oral_transform(pil_image).unsqueeze(0).to(device)
    
    with torch.no_grad():
        outputs = oral_model(image)
        probs = torch.nn.functional.softmax(outputs, dim=1)
        confidence, predicted = torch.max(probs, 1)
    
    # Get class information
    class_idx = predicted.item()
    class_info = oral_labels.get(str(class_idx), {
        'name': f'Classe {class_idx}',
        'description': 'Aucune description disponible'
    })
    
    return {
        'class': class_idx,
        'class_name': class_info['name'],
        'description': class_info['description'],
        'confidence': round(confidence.item() * 100, 2),
        'model_type': 'oral'  # Indiquer le type de modèle utilisé
    }
