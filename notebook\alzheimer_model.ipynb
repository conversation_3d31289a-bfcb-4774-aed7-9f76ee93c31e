{"cells": [{"cell_type": "code", "execution_count": null, "id": "4aae6f0a", "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torchvision import datasets, transforms, models\n", "from torch.utils.data import DataLoader\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# ========================\n", "# 1. Configuration\n", "# ========================\n", "BATCH_SIZE = 32\n", "EPOCHS = 10\n", "LEARNING_RATE = 1e-4\n", "DATA_PATH = '/kaggle/input/processed-alzheimer-disease-adni-dataset/Alzheimer_s_Disease_Neuroimaging_ADNI_Dataset'\n", "\n", "# ========================\n", "# 2. Transforms (Preprocessing)\n", "# ========================\n", "transform = transforms.Compose([\n", "    transforms.Resize((224, 224)),              # Resize all images to 224x224\n", "    transforms.ToT<PERSON>or(),                      # Convert to PyTorch Tensor\n", "    transforms.Normalize([0.5], [0.5])          # Normalize pixel values\n", "])\n", "\n", "# ========================\n", "# 3. Load Dataset\n", "# ========================\n", "train_dataset = datasets.ImageFolder(os.path.join(DATA_PATH, 'train'), transform=transform)\n", "val_dataset = datasets.ImageFolder(os.path.join(DATA_PATH, 'val'), transform=transform)\n", "test_dataset = datasets.ImageFolder(os.path.join(DATA_PATH, 'test'), transform=transform)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE)\n", "test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE)\n", "\n", "# ========================\n", "# 4. Load Pretrained Model (ResNet18)\n", "# ========================\n", "model = models.resnet18(pretrained=True)\n", "model.fc = nn.Linear(model.fc.in_features, 2)  # 2 classes: ad, mci\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model = model.to(device)\n", "\n", "# ========================\n", "# 5. Loss and Optimizer\n", "# ========================\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)\n", "\n", "# ========================\n", "# 6. Training Loop\n", "# ========================\n", "for epoch in range(EPOCHS):\n", "    model.train()\n", "    total_loss = 0\n", "    for images, labels in train_loader:\n", "        images, labels = images.to(device), labels.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        outputs = model(images)\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "\n", "    print(f\"Epoch [{epoch+1}/{EPOCHS}], Loss: {total_loss/len(train_loader):.4f}\")\n", "\n", "# ========================\n", "# 7. Validation Accuracy\n", "# ========================\n", "model.eval()\n", "correct = 0\n", "total = 0\n", "with torch.no_grad():\n", "    for images, labels in val_loader:\n", "        images, labels = images.to(device), labels.to(device)\n", "        outputs = model(images)\n", "        _, predicted = torch.max(outputs, 1)\n", "        total += labels.size(0)\n", "        correct += (predicted == labels).sum().item()\n", "\n", "print(f\"Validation Accuracy: {100 * correct / total:.2f}%\")\n", "\n", "# ========================\n", "# 8. Save Model\n", "# ========================\n", "torch.save(model.state_dict(), \"alzheimer_model.pth\")\n", "print(\"✅ Model saved as model.pth\")\n", "\n", "# ========================\n", "# 9. Optional: Test Accuracy\n", "# ========================\n", "correct = 0\n", "total = 0\n", "with torch.no_grad():\n", "    for images, labels in test_loader:\n", "        images, labels = images.to(device), labels.to(device)\n", "        outputs = model(images)\n", "        _, predicted = torch.max(outputs, 1)\n", "        total += labels.size(0)\n", "        correct += (predicted == labels).sum().item()\n", "\n", "print(f\"Test Accuracy: {100 * correct / total:.2f}%\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 5}