// Variables globales pour stocker les résultats dans différentes langues
let resultData = null;
let currentLanguage = 'fr';
let cachedTranslations = {}; // Cache pour stocker les traductions déjà récupérées

// Fonction pour initialiser le sélecteur de langue
function initLanguageSelector() {
    const langButtons = document.querySelectorAll('.lang-btn');
    const languageSelector = document.getElementById('languageSelector');
    
    // Réinitialiser l'état actif
    langButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-blue-50', 'text-blue-700');
        
        // Définir le bouton de la langue actuelle comme actif
        if (btn.getAttribute('data-lang') === currentLanguage) {
            btn.classList.add('active', 'bg-blue-50', 'text-blue-700');
        }
        
        // Ajouter les écouteurs d'événements
        btn.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            
            // Ne rien faire si on clique sur la langue déjà active
            if (lang === currentLanguage) return;
            
            // Mettre à jour l'apparence des boutons
            langButtons.forEach(b => b.classList.remove('active', 'bg-blue-50', 'text-blue-700'));
            this.classList.add('active', 'bg-blue-50', 'text-blue-700');
            
            // Changer la langue
            changeLanguage(lang);
        });
    });
    
    // Afficher le sélecteur de langue
    if (languageSelector) {
        languageSelector.classList.remove('hidden');
    }
}

// Fonction pour changer la langue des résultats
function changeLanguage(lang) {
    if (currentLanguage === lang || !resultData) return;
    
    // Vérifier si nous avons déjà cette traduction en cache
    if (cachedTranslations[lang]) {
        console.log(`Using cached translation for ${lang}`);
        currentLanguage = lang;
        showModelResults(cachedTranslations[lang], analyzedImage.src);
        return;
    }
    
    // Mettre à jour l'interface utilisateur pour indiquer la langue active
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(btn => {
        if (btn.getAttribute('data-lang') === lang) {
            btn.classList.add('active', 'bg-blue-50', 'text-blue-700');
        } else {
            btn.classList.remove('active', 'bg-blue-50', 'text-blue-700');
        }
    });
    
    // Afficher un indicateur de chargement
    showTranslationLoading(lang);
    
    // Traduire les résultats
    translateResults(lang);
}

// Fonction pour afficher l'indicateur de chargement de traduction
function showTranslationLoading(lang) {
    const detectionSummary = document.getElementById('detectionSummary');
    const conditionsList = document.getElementById('conditionsList');
    
    // Texte de chargement adapté à la langue
    let loadingText = 'Traduction en cours...';
    if (lang === 'en') loadingText = 'Translating...';
    if (lang === 'ar') loadingText = 'جاري الترجمة...';
    
    if (detectionSummary) {
        detectionSummary.innerHTML = `<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>${loadingText}</div>`;
    }
    
    if (conditionsList) {
        conditionsList.innerHTML = `<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>${loadingText}</div>`;
    }
}

// Fonction pour traduire les résultats
function translateResults(lang) {
    console.log(`Translating to: ${lang}`);
    
    // Appeler l'API pour traduire les résultats
    fetch('/api/translate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            data: resultData,
            target_language: lang
        }),
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(translatedData => {
        console.log(`Received translated data for ${lang}:`, translatedData);
        
        // Vérifier que les données traduites sont valides
        if (!translatedData || translatedData.error) {
            throw new Error(translatedData.error || "Unknown translation error");
        }
        
        // Stocker la traduction dans le cache
        cachedTranslations[lang] = translatedData;
        
        // Mettre à jour la langue actuelle
        currentLanguage = lang;
        
        // Mettre à jour l'affichage avec les données traduites
        showModelResults(translatedData, analyzedImage.src);
    })
    .catch(error => {
        console.error('Error translating results:', error);
        
        // En cas d'erreur, afficher un message et revenir à la langue précédente
        const detectionSummary = document.getElementById('detectionSummary');
        
        // Message d'erreur adapté à la langue
        let errorText = 'Erreur de traduction. Affichage en langue originale.';
        if (lang === 'en') errorText = 'Translation error. Displaying in original language.';
        if (lang === 'ar') errorText = 'خطأ في الترجمة. عرض باللغة الأصلية.';
        
        if (detectionSummary) {
            detectionSummary.innerHTML += `<div class="text-red-500 mt-2">${errorText}</div>`;
        }
        
        // Réinitialiser le bouton de langue actif
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            btn.classList.remove('active', 'bg-blue-50', 'text-blue-700');
            if (btn.getAttribute('data-lang') === currentLanguage) {
                btn.classList.add('active', 'bg-blue-50', 'text-blue-700');
            }
        });
        
        // Réafficher les résultats dans la langue précédente
        setTimeout(() => showModelResults(resultData, analyzedImage.src), 1000);
    });
}

// Modifier la fonction showModelResults pour stocker les données
function showModelResults(data, imageData) {
    // Stocker les données pour une utilisation ultérieure
    resultData = data;
    
    // Set the analyzed image
    if (analyzedImage) analyzedImage.src = imageData;
    
    // Fonction pour corriger les problèmes d'encodage UTF-8
    // ... (code existant)
    
    // Afficher les résultats
    // ... (code existant)
    
    // Afficher le sélecteur de langue
    initLanguageSelector();
    
    // Show results section
    if (resultsSection) resultsSection.classList.remove('hidden');
    
    // Scroll to results
    setTimeout(() => {
        if (resultsSection) resultsSection.scrollIntoView({ behavior: 'smooth' });
    }, 100);
}

