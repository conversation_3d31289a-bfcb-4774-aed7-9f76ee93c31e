# MRI Detection Feature

This document describes the MRI detection functionality integrated into the Flask application using Google's Gemini Vision API.

## Overview

The MRI detection feature allows users to upload medical images and determine whether they are MRI (Magnetic Resonance Imaging) scans. The system uses Google's Gemini Vision API to analyze images and provide detailed detection results.

## Features

- **Image Upload Support**: Accepts JPEG, PNG, and WebP formats
- **Multiple Input Methods**: File upload or base64 encoded images
- **Confidence Scoring**: Provides confidence scores from 0.0 to 1.0
- **Detailed Analysis**: Returns anatomical region, image characteristics, and reasoning
- **Error Handling**: Comprehensive error handling with fallback responses
- **Logging**: Detailed logging for debugging and monitoring
- **Security**: API keys stored in environment variables

## API Endpoint

### POST `/api/detect-mri`

Detects whether an uploaded image is an MRI scan.

#### Request Methods

**Method 1: File Upload (multipart/form-data)**
```bash
curl -X POST \
  http://localhost:5000/api/detect-mri \
  -F "file=@path/to/image.jpg"
```

**Method 2: Base64 Image Data (JSON)**
```bash
curl -X POST \
  http://localhost:5000/api/detect-mri \
  -H "Content-Type: application/json" \
  -d '{
    "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "format": "JPEG"
  }'
```

#### Response Format

```json
{
  "is_mri": true,
  "confidence": 0.85,
  "detected_type": "Brain MRI scan",
  "anatomical_region": "brain",
  "reasoning": "Image shows characteristic MRI features including...",
  "image_characteristics": "Grayscale medical image with high tissue contrast...",
  "api_status": "success",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "confidence_threshold": 0.7,
  "meets_threshold": true,
  "image_url": "/uploads/mri_detection_20240115103000_image.jpg?t=20240115103000"
}
```

#### Response Fields

- `is_mri` (boolean): Whether the image is detected as an MRI scan
- `confidence` (float): Confidence score between 0.0 and 1.0
- `detected_type` (string): Description of the detected image type
- `anatomical_region` (string): Body region if MRI is detected
- `reasoning` (string): Explanation of the detection decision
- `image_characteristics` (string): Description of visual features
- `api_status` (string): Status of the API call ("success", "partial_success", "error")
- `timestamp` (string): ISO timestamp of the analysis
- `confidence_threshold` (float): Configured confidence threshold
- `meets_threshold` (boolean): Whether confidence meets the threshold
- `image_url` (string): URL to access the uploaded image (file upload only)

#### HTTP Status Codes

- `200`: Successful analysis
- `206`: Partial success (fallback analysis used)
- `400`: Bad request (invalid input)
- `500`: Server error

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional
SECRET_KEY=your_secret_key_here
FLASK_DEBUG=false
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
MRI_CONFIDENCE_THRESHOLD=0.7
```

### Configuration Options

- `GEMINI_API_KEY`: Your Google Gemini API key (required)
- `MRI_CONFIDENCE_THRESHOLD`: Minimum confidence for positive MRI detection (default: 0.7)
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `LOG_FILE`: Path to log file

## Installation

1. **Install Dependencies**
```bash
pip install -r api/requirements.txt
```

2. **Set Up Environment Variables**
```bash
cp .env.example .env
# Edit .env with your API key
```

3. **Create Required Directories**
```bash
mkdir -p logs uploads
```

4. **Run the Application**
```bash
cd api
python app.py
```

## Testing

Run the test suite:

```bash
python -m pytest tests/test_mri_detection.py -v
```

Or run individual tests:

```bash
cd tests
python test_mri_detection.py
```

## Error Handling

The system includes comprehensive error handling:

- **Invalid file types**: Returns 400 with error message
- **Missing image data**: Returns 400 with error message
- **API failures**: Returns 500 with error details
- **Parsing errors**: Uses fallback analysis with reduced confidence

## Logging

All operations are logged with timestamps and details:

- Request received
- Image processing steps
- API responses
- Errors and exceptions

Logs are written to both file and console.

## Security Considerations

- API keys are stored in environment variables
- File uploads are validated for type and size
- Temporary files are cleaned up
- Input validation prevents malicious uploads

## Limitations

- Maximum file size: 16MB
- Supported formats: JPEG, PNG, WebP
- Requires internet connection for Gemini API
- Analysis quality depends on image quality and clarity

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY environment variable is required"**
   - Set the GEMINI_API_KEY in your .env file

2. **"Invalid file type"**
   - Ensure image is in JPEG, PNG, or WebP format

3. **Low confidence scores**
   - Check image quality and ensure it's a clear medical image
   - Adjust MRI_CONFIDENCE_THRESHOLD if needed

4. **API timeout errors**
   - Check internet connection
   - Verify Gemini API key is valid and has quota

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=DEBUG
FLASK_DEBUG=true
```

This will provide detailed information about the analysis process.
