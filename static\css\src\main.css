/* Ajouter ces styles à votre fichier CSS */

/* Styles pour les recommandations */
.recommendations-list {
    margin-top: 0.5rem;
}

.recommendations-list li {
    margin-bottom: 0.5rem;
    position: relative;
}

/* Style pour les recommandations avec icônes */
.recommendation-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.recommendation-item i {
    color: #4361ee;
    margin-right: 0.5rem;
    margin-top: 0.25rem;
}

/* Animation pour les recommandations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.recommendations-container {
    animation: fadeInUp 0.4s ease-out;
}

/* Style pour les différentes catégories de recommandations */
.recommendation-category {
    background-color: #f0f9ff;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-left: 3px solid #4361ee;
}

.recommendation-category-title {
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
}

/* Style pour les recommandations en arabe */
[data-lang="ar"] .recommendation-item {
    flex-direction: row-reverse;
}

[data-lang="ar"] .recommendation-item i {
    margin-right: 0;
    margin-left: 0.5rem;
}