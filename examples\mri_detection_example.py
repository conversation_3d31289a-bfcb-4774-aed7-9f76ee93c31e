#!/usr/bin/env python3
"""
Example script demonstrating MRI detection functionality
"""

import requests
import base64
import json
from PIL import Image
import io

# Configuration
API_BASE_URL = "http://localhost:5000"
MRI_DETECTION_ENDPOINT = f"{API_BASE_URL}/api/detect-mri"

def create_sample_image():
    """Create a sample grayscale image that might resemble a medical scan"""
    # Create a 256x256 grayscale image with some patterns
    image = Image.new('L', (256, 256), color=50)
    
    # Add some circular patterns to simulate brain structures
    from PIL import ImageDraw
    draw = ImageDraw.Draw(image)
    
    # Outer circle (skull)
    draw.ellipse([20, 20, 236, 236], outline=200, width=3)
    
    # Inner structures
    draw.ellipse([60, 60, 196, 196], outline=150, width=2)
    draw.ellipse([100, 100, 156, 156], outline=180, width=1)
    
    # Convert to RGB for JPEG compatibility
    return image.convert('RGB')

def image_to_base64(image, format='JPEG'):
    """Convert PIL image to base64 string"""
    buffer = io.BytesIO()
    image.save(buffer, format=format)
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return f"data:image/{format.lower()};base64,{img_str}"

def test_file_upload(image_path):
    """Test MRI detection with file upload"""
    print(f"\n=== Testing File Upload: {image_path} ===")
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(MRI_DETECTION_ENDPOINT, files=files)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 206]:
            result = response.json()
            print_results(result)
        else:
            print(f"Error: {response.text}")
            
    except FileNotFoundError:
        print(f"File not found: {image_path}")
    except Exception as e:
        print(f"Error: {e}")

def test_base64_upload(image):
    """Test MRI detection with base64 image data"""
    print("\n=== Testing Base64 Upload ===")
    
    try:
        base64_image = image_to_base64(image)
        
        payload = {
            'image': base64_image,
            'format': 'JPEG'
        }
        
        response = requests.post(
            MRI_DETECTION_ENDPOINT,
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 206]:
            result = response.json()
            print_results(result)
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

def print_results(result):
    """Print formatted detection results"""
    print("\n--- Detection Results ---")
    print(f"Is MRI: {result.get('is_mri', 'Unknown')}")
    print(f"Confidence: {result.get('confidence', 0):.2f}")
    print(f"Meets Threshold: {result.get('meets_threshold', 'Unknown')}")
    print(f"Detected Type: {result.get('detected_type', 'Unknown')}")
    print(f"Anatomical Region: {result.get('anatomical_region', 'Unknown')}")
    print(f"API Status: {result.get('api_status', 'Unknown')}")
    
    if 'reasoning' in result:
        print(f"\nReasoning: {result['reasoning']}")
    
    if 'image_characteristics' in result:
        print(f"\nImage Characteristics: {result['image_characteristics']}")
    
    if 'image_url' in result:
        print(f"\nImage URL: {API_BASE_URL}{result['image_url']}")

def test_error_cases():
    """Test various error cases"""
    print("\n=== Testing Error Cases ===")
    
    # Test with no data
    print("\n--- No Data ---")
    response = requests.post(MRI_DETECTION_ENDPOINT, json={})
    print(f"Status: {response.status_code}, Response: {response.text}")
    
    # Test with invalid base64
    print("\n--- Invalid Base64 ---")
    response = requests.post(MRI_DETECTION_ENDPOINT, json={'image': 'invalid_base64'})
    print(f"Status: {response.status_code}")

def main():
    """Main function to run all tests"""
    print("MRI Detection API Test Script")
    print("=" * 40)
    
    # Create a sample image
    sample_image = create_sample_image()
    
    # Save sample image for file upload test
    sample_path = "sample_medical_image.jpg"
    sample_image.save(sample_path)
    print(f"Created sample image: {sample_path}")
    
    # Test base64 upload
    test_base64_upload(sample_image)
    
    # Test file upload
    test_file_upload(sample_path)
    
    # Test error cases
    test_error_cases()
    
    print("\n=== Test Complete ===")
    print(f"Sample image saved as: {sample_path}")
    print("You can use this image for further testing.")

if __name__ == "__main__":
    main()
