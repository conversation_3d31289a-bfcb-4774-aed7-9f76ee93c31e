import os
import torch
import json
from torchvision import transforms
import torch.nn as nn
from PIL import Image
from torchvision.models import resnet18

# Chemin absolu sécurisé
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_PATH = os.path.join(BASE_DIR, 'models', 'alzheimer_model.pth')
LABELS_PATH = os.path.join(BASE_DIR, 'models', 'alzheimer_labels.json')

# Vérification des fichiers
if not os.path.exists(MODEL_PATH):
    print(f"Avertissement: Modèle Alzheimer introuvable à l'emplacement: {MODEL_PATH}")
    print("Le modèle sera chargé à la demande.")

if not os.path.exists(LABELS_PATH):
    # Créer le fichier de labels par défaut
    alzheimer_labels = {
        "0": {"name": "AD (Alzheimer's Disease)", "description": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>, caractérisée par une dégénérescence progressive des cellules nerveuses et une atrophie cérébrale."},
        "1": {"name": "MCI (Mild Cognitive Impairment)", "description": "Trouble cognitif léger, un stade intermédiaire entre le déclin cognitif normal lié à l'âge et la démence plus grave."}
    }
    
    os.makedirs(os.path.dirname(LABELS_PATH), exist_ok=True)
    with open(LABELS_PATH, 'w') as f:
        json.dump(alzheimer_labels, f, indent=4)
    print(f"Fichier de labels créé à: {LABELS_PATH}")

# Définition du modèle (basé sur ResNet18)
class AlzheimerModel(nn.Module):
    def __init__(self, num_classes=2):
        super(AlzheimerModel, self).__init__()
        # Utiliser ResNet18 comme base
        self.model = resnet18(weights=None)
        # Remplacer la dernière couche fully connected pour notre classification
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        return self.model(x)

# Variable pour stocker le modèle chargé
alzheimer_model = None
device = None

def load_alzheimer_model():
    """
    Charge le modèle de classification Alzheimer
    """
    global alzheimer_model, device
    
    # Si le modèle est déjà chargé, le retourner
    if alzheimer_model is not None:
        return alzheimer_model, device
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    alzheimer_model = AlzheimerModel(num_classes=2)
    
    # Vérifier si le fichier du modèle existe
    if os.path.exists(MODEL_PATH):
        try:
            alzheimer_model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
            print("Modèle Alzheimer chargé avec succès.")
        except Exception as e:
            print(f"Erreur lors du chargement du modèle Alzheimer: {e}")
    else:
        print("Modèle Alzheimer non trouvé. Utilisation d'un modèle non entraîné.")
    
    alzheimer_model.eval()
    return alzheimer_model, device

# Charger les labels
with open(LABELS_PATH) as f:
    alzheimer_labels = json.load(f)

# Transformation pour les images
alzheimer_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.5], [0.5])
])

def predict_alzheimer_image(pil_image):
    """
    Predict the class of an Alzheimer's disease image using the loaded model
    
    Args:
        pil_image: PIL Image object or path to image file
    
    Returns:
        dict: Prediction results with class, class_name, description, and confidence
    """
    # Charger le modèle à la demande
    global alzheimer_model, device
    if alzheimer_model is None:
        alzheimer_model, device = load_alzheimer_model()
    
    # If pil_image is a string (file path), open the image
    if isinstance(pil_image, str):
        pil_image = Image.open(pil_image).convert('RGB')
    elif not isinstance(pil_image, Image.Image):
        raise TypeError("Input must be a PIL Image or a file path")
    
    # Ensure image is in RGB mode
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    # Apply transformations and get prediction
    image = alzheimer_transform(pil_image).unsqueeze(0).to(device)
    
    with torch.no_grad():
        outputs = alzheimer_model(image)
        probs = torch.nn.functional.softmax(outputs, dim=1)
        confidence, predicted = torch.max(probs, 1)
    
    # Get class information
    class_idx = predicted.item()
    class_info = alzheimer_labels.get(str(class_idx), {
        'name': f'Classe {class_idx}',
        'description': 'Aucune description disponible'
    })
    
    return {
        'class': class_idx,
        'class_name': class_info['name'],
        'description': class_info['description'],
        'confidence': round(confidence.item() * 100, 2),
        'model_type': 'alzheimer'  # Indiquer le type de modèle utilisé
    }
